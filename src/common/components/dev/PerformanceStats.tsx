'use client';

import { useState, useEffect } from 'react';
import { performanceMonitor } from '@/common/libs/performance-monitor';

interface PerformanceStatsProps {
  show?: boolean;
}

export default function PerformanceStats({ show = false }: PerformanceStatsProps) {
  const [isVisible, setIsVisible] = useState(show);
  const [stats, setStats] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);

  useEffect(() => {
    if (isVisible) {
      // 获取性能统计
      const performanceStats = performanceMonitor.getMetrics();
      setStats(performanceStats);

      // 获取缓存统计 - 通过 API 调用
      try {
        fetch('/api/cache-stats')
          .then(res => res.json())
          .then(data => setCacheStats(data))
          .catch(error => console.error('Error getting cache stats:', error));
      } catch (error) {
        console.error('Error getting cache stats:', error);
      }
    }
  }, [isVisible]);

  // 在开发环境下显示快捷键提示
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + P 切换性能面板
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        setIsVisible(!isVisible);
      }
    };

    if (process.env.NODE_ENV === 'development') {
      window.addEventListener('keydown', handleKeyPress);
      return () => window.removeEventListener('keydown', handleKeyPress);
    }
  }, [isVisible]);

  if (!isVisible || process.env.NODE_ENV !== 'development') {
    return null;
  }

  const generateReport = () => {
    const report = performanceMonitor.generateReport();
    console.log(report);
    alert('Performance report logged to console');
  };

  const clearStats = () => {
    performanceMonitor.clear();
    setStats([]);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-black/90 text-white p-4 rounded-lg max-w-md max-h-96 overflow-auto text-xs">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-sm">Performance Stats</h3>
        <div className="flex gap-2">
          <button
            onClick={generateReport}
            className="px-2 py-1 bg-blue-600 rounded text-xs hover:bg-blue-700"
          >
            Report
          </button>
          <button
            onClick={clearStats}
            className="px-2 py-1 bg-red-600 rounded text-xs hover:bg-red-700"
          >
            Clear
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="px-2 py-1 bg-gray-600 rounded text-xs hover:bg-gray-700"
          >
            ×
          </button>
        </div>
      </div>

      {/* 缓存统计 */}
      {cacheStats && (
        <div className="mb-3 p-2 bg-gray-800 rounded">
          <h4 className="font-semibold mb-1">Cache Stats</h4>
          <div>Cache Size: {cacheStats.cacheSize}</div>
          <div>File Stats: {cacheStats.fileStatsSize}</div>
          {cacheStats.cacheEntries.length > 0 && (
            <div className="mt-1">
              <div className="font-medium">Cached Collections:</div>
              {cacheStats.cacheEntries.map((key: string, index: number) => (
                <div key={index} className="text-gray-300 ml-2">
                  {key}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 性能指标 */}
      {stats && stats.length > 0 ? (
        <div className="space-y-2">
          <h4 className="font-semibold">Recent Operations</h4>
          {stats.slice(-10).map((metric: any, index: number) => (
            <div key={index} className="p-2 bg-gray-800 rounded">
              <div className="font-medium">{metric.operation}</div>
              <div className="text-gray-300">
                Duration: {metric.duration?.toFixed(2)}ms
              </div>
              {metric.metadata && (
                <div className="text-gray-400 text-xs">
                  {JSON.stringify(metric.metadata)}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-gray-400">No performance data available</div>
      )}

      <div className="mt-3 pt-2 border-t border-gray-600 text-gray-400 text-xs">
        Press Ctrl+Shift+P to toggle this panel
      </div>
    </div>
  );
}
