import fs from 'fs';
import matter from 'gray-matter';
import path from 'path';
import { remark } from 'remark';
import remarkGfm from 'remark-gfm';
import remarkMdx from 'remark-mdx';
import remarkParse from 'remark-parse';

import { calculateReadingTime, formatExcerpt } from '@/common/helpers';
import { getTimestamp } from '@/common/helpers';

export interface MdxFileProps {
  slug: string;
  frontMatter: Record<string, unknown>;
  content?: string;
  props?: {
    readingTimeMinutes: number;
    description: string;
  };
}

// 缓存接口
interface CacheEntry {
  data: MdxFileProps[];
  timestamp: number;
  withContent: boolean;
}

// 内存缓存
const cache = new Map<string, CacheEntry>();
const CACHE_TTL = 1000 * 60 * 5; // 5分钟缓存

// 文件修改时间缓存，用于检测文件变化
const fileStatsCache = new Map<string, number>();

// 检查目录是否有文件变化
const hasDirectoryChanged = (dirPath: string): boolean => {
  try {
    const files = fs.readdirSync(dirPath, { recursive: true });
    const mdxFiles = (files as string[]).filter((file) =>
      file.endsWith('.mdx') || file.endsWith('.md')
    );

    for (const file of mdxFiles) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      const lastModified = stats.mtime.getTime();
      const cachedTime = fileStatsCache.get(filePath);

      if (!cachedTime || cachedTime !== lastModified) {
        // 更新缓存
        fileStatsCache.set(filePath, lastModified);
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Error checking directory changes:', error);
    return true; // 出错时假设有变化
  }
};

// 获取缓存键
const getCacheKey = (dirPath: string, withContent: boolean): string => {
  return `${dirPath}:${withContent}`;
};

// 检查缓存是否有效
const isCacheValid = (cacheEntry: CacheEntry): boolean => {
  const now = Date.now();
  return (now - cacheEntry.timestamp) < CACHE_TTL;
};

export const _loadMdxFiles = (dirPath: string, withContent = true): MdxFileProps[] => {
  try {
    console.log(`Loading MDX files from: ${dirPath}`);
    if (!fs.existsSync(dirPath)) {
      console.error(`Directory does not exist: ${dirPath}`);
      return [];
    }

    const files = fs.readdirSync(dirPath, { recursive: true });
    console.log(`Found ${files.length} total files in directory`);

    const mdxFiles = (files as string[]).filter((file) => {
      // 同时允许 md 和 mdx 文件
      return file.endsWith('.mdx') || file.endsWith('.md');
    });

    console.log(`Found ${mdxFiles.length} MDX/MD files`);

    const contents = mdxFiles.map((file) => {
      try {
        const filePath = path.join(dirPath, file);
        console.log(`Processing file: ${filePath}`);

        const source = fs.readFileSync(filePath, 'utf-8');
        const { content, data } = matter(source);

        const filename = file.endsWith('.mdx')
          ? file.replace('.mdx', '')
          : file.replace('.md', '');

        if (!data.featured_image_url) {
          const randomSrc = `/images/bg/bg${Math.floor(
            Math.random() * 15
          )}.svg`;
          data.featured_image_url = randomSrc;
        }

        // 基础数据结构
        const baseData = {
          slug: data.slug || filename,
          frontMatter: data,
        };

        // 只有需要内容时才进行 MDX 编译
        if (withContent) {
          const mdxCompiler = remark()
            .use(remarkParse)
            .use(remarkGfm)
            .use(remarkMdx);
          const mdxContent = mdxCompiler.processSync(content).toString();

          return {
            ...baseData,
            content: mdxContent,
            props: {
              readingTimeMinutes: calculateReadingTime(mdxContent) ?? 0,
              description: formatExcerpt(mdxContent) || '',
            },
          };
        } else {
          // 不需要内容时，只返回基础信息
          return {
            ...baseData,
            content: '',
            props: {
              readingTimeMinutes: 0,
              description: data.description || data.summary || '',
            },
          };
        }
      } catch (error) {
        console.error(`Error processing file ${file}:`, error);
        // Return a minimal valid object to avoid breaking the entire collection
        return {
          slug: file,
          frontMatter: {
            title: `Error loading ${file}`,
            date: new Date().toISOString(),
            tags: ['error'],
            featured_image_url: '/images/bg/bg0.svg',
          },
          content: '',
          props: {
            readingTimeMinutes: 0,
            description: 'Error loading this file',
          },
        };
      }
    });

    console.log(`Successfully processed ${contents.length} MDX files`);
    return contents;
  } catch (error) {
    console.error(`Error in _loadMdxFiles:`, error);
    return [];
  }
};

export const getCollectionCount = (endpointer: string): number => {
  const dirPath = path.join(process.cwd(), 'src/contents', endpointer);
  const files = fs.readdirSync(dirPath);
  const mdxFiles = files.filter(
    (file) => file.endsWith('.mdx') || file.endsWith('.md')
  );
  return mdxFiles.length;
};

export const getCollection = (
  endpointer: string,
  withContent = false
): MdxFileProps[] => {
  try {
    console.log(`Getting collection from: ${endpointer}, withContent: ${withContent}`);
    const dirPath = path.join(process.cwd(), 'src/contents', endpointer);
    console.log(`Full directory path: ${dirPath}`);

    // Check if directory exists
    if (!fs.existsSync(dirPath)) {
      console.error(`Directory does not exist: ${dirPath}`);
      return [];
    }

    // 检查缓存
    const cacheKey = getCacheKey(dirPath, withContent);
    const cachedEntry = cache.get(cacheKey);

    if (cachedEntry && isCacheValid(cachedEntry) && !hasDirectoryChanged(dirPath)) {
      console.log(`Using cached data for ${endpointer}`);
      return cachedEntry.data;
    }

    // 加载文件，传递 withContent 参数
    const files = _loadMdxFiles(dirPath, withContent);
    console.log(`Loaded ${files.length} files from ${endpointer}`);

    // 排序
    files.sort((a, b) => {
      return getTimestamp(a.frontMatter.date as string) <
        getTimestamp(b.frontMatter.date as string)
        ? 1
        : -1;
    });

    // 缓存结果
    cache.set(cacheKey, {
      data: files,
      timestamp: Date.now(),
      withContent,
    });

    return files;
  } catch (error) {
    console.error(`Error in getCollection(${endpointer}):`, error);
    return [];
  }
};

export const getEntry = (
  endpointer: string,
  slug: string
): MdxFileProps | null => {
  // 首先尝试从缓存中获取不包含内容的集合
  const collection = getCollection(endpointer, false);
  const entry = collection.find((item) => item.slug === slug);

  if (!entry) {
    return null;
  }

  // 如果找到了条目但没有内容，则单独加载该文件的内容
  if (!entry.content) {
    const dirPath = path.join(process.cwd(), 'src/contents', endpointer);
    const fullCollection = _loadMdxFiles(dirPath, true);
    const fullEntry = fullCollection.find((item) => item.slug === slug);
    return fullEntry || entry;
  }

  return entry;
};

// 单独加载单个文件的优化函数
export const loadSingleMdxFile = (filePath: string, withContent = true): MdxFileProps | null => {
  try {
    if (!fs.existsSync(filePath)) {
      return null;
    }

    const source = fs.readFileSync(filePath, 'utf-8');
    const { content, data } = matter(source);

    const filename = path.basename(filePath).replace(/\.(mdx|md)$/, '');

    if (!data.featured_image_url) {
      const randomSrc = `/images/bg/bg${Math.floor(Math.random() * 15)}.svg`;
      data.featured_image_url = randomSrc;
    }

    const baseData = {
      slug: data.slug || filename,
      frontMatter: data,
    };

    if (withContent) {
      const mdxCompiler = remark()
        .use(remarkParse)
        .use(remarkGfm)
        .use(remarkMdx);
      const mdxContent = mdxCompiler.processSync(content).toString();

      return {
        ...baseData,
        content: mdxContent,
        props: {
          readingTimeMinutes: calculateReadingTime(mdxContent) ?? 0,
          description: formatExcerpt(mdxContent) || '',
        },
      };
    } else {
      return {
        ...baseData,
        content: '',
        props: {
          readingTimeMinutes: 0,
          description: data.description || data.summary || '',
        },
      };
    }
  } catch (error) {
    console.error(`Error loading single MDX file ${filePath}:`, error);
    return null;
  }
};

// 缓存管理函数
export const clearCache = (): void => {
  cache.clear();
  fileStatsCache.clear();
  console.log('MDX cache cleared');
};

export const getCacheStats = () => {
  return {
    cacheSize: cache.size,
    fileStatsSize: fileStatsCache.size,
    cacheEntries: Array.from(cache.keys()),
  };
};

// 清理过期缓存
export const cleanupExpiredCache = (): void => {
  const keysToDelete: string[] = [];
  cache.forEach((entry, key) => {
    if (!isCacheValid(entry)) {
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => cache.delete(key));
};

/**
 * 读取站点设置文件内容
 * 用于服务器组件中获取设置
 */
export const readSettingsFile = async (): Promise<string> => {
  try {
    const filePath = path.join(process.cwd(), 'src/contents/siteMetadata.ts');
    return fs.readFileSync(filePath, 'utf-8');
  } catch (error) {
    console.error('Error reading settings file:', error);
    return '';
  }
};
