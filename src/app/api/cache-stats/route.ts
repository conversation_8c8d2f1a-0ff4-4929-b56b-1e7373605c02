import { NextResponse } from 'next/server';
import { getCacheStats } from '@/common/libs/mdx';

export async function GET() {
  try {
    // 只在开发环境提供缓存统计
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 404 });
    }

    const stats = getCacheStats();
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return NextResponse.json({ error: 'Failed to get cache stats' }, { status: 500 });
  }
}
